// --- File: components/ScriptReader/ScriptMarkdownContent.tsx ---

import React from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import { ExternalLink, Copy, Check } from 'lucide-react'
import { <PERSON>rism as Syntax<PERSON><PERSON>lighter } from 'react-syntax-highlighter' // Keep if code blocks are possible

interface ScriptMarkdownProps {
  content: string
  onItemClick?: (text: string) => void // Keep if list item clicks are needed elsewhere
  customTheme?: any // Keep for syntax highlighting
  onCopyCode?: (code: string) => void // Keep for code blocks
  baseUrl?: string
}

export default function ScriptMarkdownContent({
  content,
  onItemClick, // Pass down if needed by lists
  customTheme, // Pass down if needed by code blocks
  onCopyCode, // Pass down if needed by code blocks
  baseUrl = typeof window !== 'undefined' ? window.location.origin : '',
}: ScriptMarkdownProps) {
  const [copiedCodeBlock, setCopiedCodeBlock] = React.useState<string | null>(null)

  // --- Remove or simplify the preprocessing logic ---
  // This logic was designed for the old "Content:" format.
  // If the new formatting from markdownFormatterTool is reliable,
  // this preprocessing might not be needed or should be adapted.
  // For now, let's bypass it and directly use the content assuming
  // it's correctly formatted markdown from the tool.
  const processedContent = content; // Use content directly

  /* --- Original Preprocessing Logic (commented out or removed) ---
  const processedContent = React.useMemo(() => {
     // ... complex logic to parse "Content:" format ...
     return content; // Simplified return for now
  }, [content]);
  */

  // --- Helper functions (isExternalUrl, handleCopyCodeBlock, handleListItemClick, extractText)
  // --- can remain largely the same if their features (external links, code copy, list clicks)
  // --- are still potentially used in the markdown.

  const isExternalUrl = (url?: string): boolean => { /* ... keep ... */ return false } // Simplified placeholder
  const handleCopyCodeBlock = async (codeContent: string) => { /* ... keep ... */ }
  const handleListItemClick = (text: string) => { /* ... keep ... */ }
  const extractText = (child: React.ReactNode): string => { /* ... keep ... */ return '' } // Simplified placeholder


  const markdownComponents: // Use 'any' for brevity or import Components from 'react-markdown'
    React.ComponentProps<typeof ReactMarkdown>['components'] = {

    // Code blocks (keep if needed)
    code: ({ inline, className, children, ...props }: any) => {
       // ... logic for syntax highlighting and copy button ...
       // Simplified example:
       return <code className={`inline-block bg-gray-200 text-black px-1 rounded ${className || ''}`} {...props}>{children}</code>;
    },

    // Paragraphs - Now centered for dialogue text
    p: ({ children }: { children?: React.ReactNode }) => {
      const childrenStr = String(children || '');

      // Check if this paragraph contains "Sounds" text
      if (childrenStr.includes('Sounds')) {
        return (
          <p className="mb-3 last:mb-0 leading-relaxed text-center inline-block bg-black text-lime-400 px-2 py-1">
            {children}
          </p>
        );
      }

      return (
        <p className="mb-3 last:mb-0 leading-relaxed text-black text-center">
          {children}
        </p>
      );
    },

    // Lists - Keep simple styling for metadata character lists etc.
    ul: ({ children }: { children?: React.ReactNode }) => (
      <ul className="list-disc pl-6 mb-4 space-y-1 text-black" role="list">{children}</ul>
    ),
    ol: ({ children }: { children?: React.ReactNode }) => (
      <ol className="list-decimal pl-6 mb-4 space-y-1 text-black" role="list">{children}</ol>
    ),
    // List items - Simplified, remove character parsing logic
    li: ({ children, ...props }: { children?: React.ReactNode }) => {
      // Basic list item, potentially clickable if onItemClick is used
      const text = extractText(children); // Extract text if click handler needs it
      return (
        <li
          onClick={onItemClick ? () => handleListItemClick(text) : undefined}
          className={`mb-1 ${onItemClick ? 'cursor-pointer hover:text-gray-700' : ''} text-black`}
          {...props}
        >
          {children}
        </li>
      );
    },


    // Links (keep if needed)
    a: ({ href, children }: { href?: string; children?: React.ReactNode }) => {
       // ... logic for external link indicator ...
       // Simplified example:
       return <a href={href} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{children}</a>
    },

    // Blockquotes (keep if needed)
    blockquote: ({ children }: { children?: React.ReactNode }) => (
      <blockquote className="border-l-4 border-gray-300 pl-4 py-1 my-4 italic text-gray-700">
        {children}
      </blockquote>
    ),

    // Strong text - Simplified, just bold. Remove character name detection.
    strong: ({ children }: { children?: React.ReactNode }) => {
      // Check if content is '**CharacterName**' from metadata list, style differently? Optional.
      const childrenStr = String(children || '');
      const isMetadataChar = /^[A-Z][A-Z\s]+$/.test(childrenStr) && childrenStr.length > 1; // Basic check

      return (
        <strong className="font-semibold text-black">
          {children}
        </strong>
      );
    },

    // Emphasized text (italics) - Keep styling for notes
    em: ({ children }: { children?: React.ReactNode }) => {
      // Style specifically for notes (often italicized in markdown)
      return (
        <em className="italic text-gray-600">
          {children}
        </em>
      );
    },

    // Headings
    h1: ({ children }: { children?: React.ReactNode }) => (
      <h1 className="text-2xl font-bold mb-6 text-black border-b pb-2">{children}</h1> // Adjusted style
    ),
    // H2 - Differentiate between Metadata Headers and Character Names
    h2: ({ children }: { children?: React.ReactNode }) => {
      const childrenStr = String(children || '');
      // Assume headers like "Script", "Summary", "Characters" are metadata headers
      const isMetadataHeader = ['Script', 'Summary', 'Characters'].includes(childrenStr);

      if (!isMetadataHeader) {
           // Apply character name styling (using the existing class or direct styles)
          return (
              // Use the dedicated class for styling consistency
              <div className="character-dialogue">
                  {/* Render the character name */}
                  <strong>{children}</strong>
              </div>
          );
      }

      // Default H2 styling for section headers like "Script", "Summary"
      return (
          <h2 className="text-xl font-semibold mb-4 mt-6 text-black border-b border-gray-200 pb-2">{children}</h2>
      );
    },
    // H3 - For metadata like "By Author"
    h3: ({ children }: { children?: React.ReactNode }) => (
      <h3 className="text-lg font-medium mb-4 text-gray-700">{children}</h3>
    ),
  }

  return (
    // Ensure the container provides a white background as per the target image
    <div className="prose max-w-none bg-white p-6 rounded-lg shadow-md mb-4 mx-auto script-content-container">

      <ReactMarkdown
          rehypePlugins={[rehypeRaw]} // Allows processing HTML within markdown if needed (like fallback)
          components={markdownComponents}
      >
          {processedContent}
      </ReactMarkdown>

      {/* Define Styles matching the target image */}
      <style jsx global>{`
        /* Script content container - Adjust max-width as needed */
        .script-content-container {
          max-width: 700px; /* Restore wider width */
          margin-left: auto;
          margin-right: auto;
          font-family: "Courier New", Courier, monospace; /* Typewriter font */
          color: #333; /* Default text color */
          padding-top: 1rem; /* Add some top padding */
        }

        /* First character name in the script */
        .script-content-container h2:first-of-type {
          margin-top: 1rem; /* Less margin for first character */
        }

        /* Character name styling */
        .character-dialogue {
          text-align: center;
          margin-top: 2.5rem; /* Increased space above character name */
          margin-bottom: 0.25rem; /* Minimal space below character name */
          font-size: 1.25rem; /* Larger size */
          font-weight: 600; /* Make it bold/semibold */
          color: #000; /* Darker color for better contrast */
          padding-bottom: 0; /* Remove padding */
          font-family: "Courier New", Courier, monospace; /* Typewriter font */
          /* No border to match the screenshot */
        }

        /* Ensure paragraphs (dialogue) following character names have correct spacing */
        .character-dialogue + p {
          margin-top: 0; /* Reset top margin if needed */
          padding-top: 0; /* Remove any padding */
          margin-bottom: 2rem; /* Increase space after dialogue paragraph */
        }

        /* Styling for paragraphs (dialogue, notes) */
        .script-content-container p {
           line-height: 1.5; /* Slightly reduced line height */
           color: #333; /* Ensure dialogue text color */
           text-align: center; /* Center all dialogue text */
           max-width: 90%; /* Limit width for better readability */
           margin-left: auto;
           margin-right: auto;
           margin-top: 0; /* Remove top margin */
           margin-bottom: 1.25rem; /* Space after paragraphs */
        }

        /* Styling for italicized notes */
        .script-content-container em {
           color: #555; /* Slightly different color for notes */
           display: block; /* Make notes appear on their own line */
           margin-top: 0.25rem;
           margin-bottom: 1rem;
        }

        /* Styling for metadata lists (Characters) */
        .script-content-container ul {
          margin-top: 0.5rem;
          margin-bottom: 1.5rem;
          padding-left: 2rem; /* Indent list */
        }
        .script-content-container li {
          margin-bottom: 0.25rem;
        }

        /* Adjust heading styles if needed */
        .script-content-container h2 { /* Script, Summary Headers */
           margin-top: 2rem;
           margin-bottom: 1rem;
           padding-bottom: 0.25rem;
           border-bottom: 1px solid #ddd;
           color: #111;
        }
         .script-content-container h3 { /* By Author */
            margin-top: 0.5rem;
            margin-bottom: 1.5rem;
            font-style: italic;
            color: #444;
         }

        /* Remove styles for elements no longer used in this format */
        .line-number { display: none; }
        .script-dialogue strong { /* If .script-dialogue class was used before */
           /* Reset any specific styles if needed */
        }
      `}</style>
    </div>
  )
}