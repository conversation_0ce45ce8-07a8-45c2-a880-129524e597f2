// app/providers.tsx
'use client';

import { ReactNode } from "react";
import { SessionProvider } from "next-auth/react";
import { SelectedDocProvider } from "components/SelectedDocContext";
import { AnalyticsProvider } from "lib/analytics/analyticsProvider";

export function Providers({ children }: { children: ReactNode }) {
  return (
    <SessionProvider>
      <SelectedDocProvider>
        <AnalyticsProvider>
          {children}
        </AnalyticsProvider>
      </SelectedDocProvider>
    </SessionProvider>
  );
}