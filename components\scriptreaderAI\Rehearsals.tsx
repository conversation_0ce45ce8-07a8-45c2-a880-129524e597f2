import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, Lo<PERSON>, MicOff, Volume2, VolumeX, Info, Settings } from 'lucide-react';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import VoiceCarousel from './VoiceCarousel';

interface RehearsalsProps {
  apiConfigStatus: 'unchecked' | 'valid' | 'invalid' | 'connecting';
  detailedErrorInfo: string | null;
  isListening: boolean;
  voiceStatus: string;
  isMuted: boolean;
  isSpeaking: boolean;
  hasPermission: boolean;
  voiceErrorMessage: string;
  toggleMute: () => Promise<void>;
  handleEndConversation: () => Promise<void>;
  handleStartConversation: () => Promise<void>;
  setVoiceErrorMessage: (message: string) => void;
  selectedScriptName?: string;
  selectedVoiceId: string | null;
  onVoiceSelect: (voiceId: string) => void;
}

const Rehearsals: React.FC<RehearsalsProps> = ({
  apiConfigStatus,
  detailedErrorInfo,
  isListening,
  voiceStatus,
  isMuted,
  isSpeaking,
  hasPermission,
  voiceErrorMessage,
  toggleMute,
  handleEndConversation,
  handleStartConversation,
  setVoiceErrorMessage,
  selectedScriptName,
  selectedVoiceId,
  onVoiceSelect
}) => {
  const { data: session } = useSession();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <div className="relative h-full">
      {/* Small microphone icon in top-right corner */}
      <div className="absolute top-4 right-4 z-10">
        <button
          onClick={() => setIsSidebarOpen(true)}
          className="w-8 h-8 flex items-center justify-center rounded-full bg-purple-600/20 border border-purple-500/30 hover:bg-purple-600/30 transition-colors"
        >
          <Mic className="w-4 h-4 text-purple-400" />
        </button>
      </div>

      {/* Main Content Area */}
      <div className="flex flex-col h-full overflow-y-auto">
        {/* Top Section with Logo and Script Info */}
        <div className="flex flex-col items-center justify-center py-8 space-y-6">
          {/* Logo Images */}
          {/* <div className="flex items-center space-x-4">
            <Image
              src="/Clogo6A.png"
              alt="CastMate Logo 1"
              width={60}
              height={60}
              className="object-contain"
            />
            <Image
              src="/Clogo7A.png"
              alt="CastMate Logo 2"
              width={60}
              height={60}
              className="object-contain"
            />
          </div> */}

          {/* Script Ready Text */}
          <div className="text-center space-y-4">
            <h2 className="text-xl font-bold text-white">
               {selectedScriptName || "No script selected"}
            </h2>

            {/* Selected Script Name Container */}
            {/* <div className="bg-black/30 backdrop-blur-sm rounded-lg p-3 border border-white/10 min-w-[250px]">
              <p className="text-base text-purple-300 font-medium">
                {selectedScriptName || "No script selected"}
              </p>
            </div> */}
          </div>
        </div>

        {/* Voice Selection Section */}
        <div className="flex-1 px-6 pb-8">
          <VoiceCarousel
            selectedVoiceId={selectedVoiceId}
            onVoiceSelect={onVoiceSelect}
          />
        </div>
      </div>

      {/* Sliding Sidebar */}
      <AnimatePresence>
        {isSidebarOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setIsSidebarOpen(false)}
            />

            {/* Sidebar */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed right-0 top-0 h-full w-80 bg-black/90 backdrop-blur-sm border-l border-white/10 z-50 overflow-y-auto"
            >


              <div className="p-6">
                {/* Sidebar Header */}
                <div className="flex items-center mb-6">
                  <Settings className="w-5 h-5 text-purple-400 mr-2" />
                  <h3 className="text-lg font-semibold text-white">Settings</h3>
                </div>

                {/* Connection Status */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-purple-400 mb-3">Connection Status</h4>
                  <div className="flex items-center space-x-3">
                    {/* Microphone icon to the left of status box */}
                    <button
                      onClick={() => setIsSidebarOpen(false)}
                      className="w-8 h-8 flex items-center justify-center rounded-full bg-purple-600/20 border border-purple-500/30 hover:bg-purple-600/30 transition-colors flex-shrink-0"
                    >
                      <Mic className="w-4 h-4 text-purple-400" />
                    </button>

                    {/* Status box */}
                    <div className="bg-black/30 rounded-lg p-3 border border-white/10 flex-1">
                      <div className="flex items-center space-x-2">
                        <span className={`w-2 h-2 rounded-full ${
                          voiceStatus === "connected"
                            ? "bg-green-400"
                            : voiceStatus === "connecting"
                              ? "bg-yellow-400"
                              : "bg-red-400"
                        }`}></span>
                        <span className={`text-sm ${
                          voiceStatus === "connected"
                            ? "text-green-400"
                            : voiceStatus === "connecting"
                              ? "text-yellow-400"
                              : "text-red-400"
                        }`}>
                          {voiceStatus === "connected"
                            ? "Connected"
                            : voiceStatus === "connecting"
                              ? "Connecting..."
                              : "Disconnected"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Voice Controls */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-purple-400 mb-3">Voice Controls</h4>
                  <div className="space-y-3">
                    <button
                      onClick={toggleMute}
                      disabled={voiceStatus !== "connected"}
                      className={`w-full p-3 rounded-lg flex items-center justify-center space-x-2 ${
                        voiceStatus === "connected"
                          ? (isMuted ? "bg-gray-700 hover:bg-gray-600" : "bg-purple-600 hover:bg-purple-700")
                          : "bg-gray-700 opacity-50 cursor-not-allowed"
                      } transition-colors`}
                      title={isMuted ? "Unmute" : "Mute"}
                    >
                      {isMuted ? (
                        <VolumeX className="h-5 w-5 text-white" />
                      ) : (
                        <Volume2 className="h-5 w-5 text-white" />
                      )}
                      <span className="text-white">{isMuted ? "Unmute" : "Mute"}</span>
                    </button>

                    {voiceStatus === "connected" ? (
                      <button
                        onClick={handleEndConversation}
                        className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-lg shadow-red-600/20 transition-colors"
                      >
                        <MicOff className="h-5 w-5" />
                        Stop Rehearsing
                      </button>
                    ) : (
                      <button
                        onClick={handleStartConversation}
                        className={`w-full flex items-center justify-center gap-2 px-6 py-3 text-white rounded-lg shadow-lg transition-colors ${
                          !hasPermission || apiConfigStatus === 'invalid' || !session?.user?.email || apiConfigStatus === 'connecting'
                            ? "bg-gray-700 opacity-50 cursor-not-allowed"
                            : "bg-purple-600 hover:bg-purple-700 shadow-purple-600/20"
                        }`}
                        disabled={!hasPermission || apiConfigStatus === 'invalid' || !session?.user?.email || apiConfigStatus === 'connecting'}

                      >
                        {apiConfigStatus === 'connecting' ? (
                          <Loader className="h-5 w-5 animate-spin" />
                        ) : (
                          <Mic className="h-5 w-5" />
                        )}
                        Start Rehearsing
                      </button>
                    )}
                  </div>
                </div>

                {/* Voice Status Indicators */}
                {voiceStatus === "connected" && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-purple-400 mb-3">Status</h4>
                    <div className="bg-black/30 rounded-lg p-3 border border-white/10">
                      {isSpeaking ? (
                        <span className="text-green-400 text-sm">AI Assistant is speaking...</span>
                      ) : isListening ? (
                        <span className="text-purple-400 text-sm">Listening to your lines...</span>
                      ) : (
                        <span className="text-gray-400 text-sm">Ready to listen</span>
                      )}
                    </div>
                  </div>
                )}

                {/* Error Messages */}
                {voiceErrorMessage && (
                  <div className="mb-6">
                    <div className="text-red-400 text-sm bg-red-500/10 p-3 rounded-lg border border-red-500/20">
                      {voiceErrorMessage}
                      <button
                        onClick={() => setVoiceErrorMessage("")}
                        className="ml-2 text-purple-400 hover:text-purple-300 underline"
                      >
                        Dismiss
                      </button>
                    </div>
                  </div>
                )}

                {!hasPermission && (
                  <div className="mb-6">
                    <div className="text-yellow-400 text-sm bg-yellow-500/10 p-3 rounded-lg border border-yellow-500/20">
                      Please allow microphone access to use rehearsal mode
                    </div>
                  </div>
                )}

                {/* API Configuration Issues */}
                {apiConfigStatus === 'invalid' && (
                  <div className="mb-6">
                    <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                      <h4 className="text-red-400 font-medium mb-2">ElevenLabs API Configuration Issue</h4>
                      <p className="text-red-300 text-xs mb-3">Voice features are unavailable due to API configuration issues.</p>

                      {detailedErrorInfo && (
                        <div className="mt-2 p-2 bg-black/30 rounded border border-red-500/10 text-xs text-red-300 font-mono overflow-x-auto">
                          {detailedErrorInfo}
                        </div>
                      )}

                      <div className="mt-3 text-xs text-gray-400">
                        <p>Recommended troubleshooting steps:</p>
                        <ol className="list-decimal ml-4 mt-1 space-y-1">
                          <li>Verify ElevenLabs API key is set in environment variables</li>
                          <li>Ensure the Agent ID is correct and active</li>
                          <li>Check network connectivity to ElevenLabs services</li>
                          <li>Verify account subscription status and API limits</li>
                        </ol>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Rehearsals;