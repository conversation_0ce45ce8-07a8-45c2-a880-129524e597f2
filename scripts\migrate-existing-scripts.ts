/**
 * Migration script to create formatted versions of existing scripts
 * Run this script to batch process existing scripts in the system
 */

import { 
  collection, 
  getDocs, 
  query, 
  where, 
  doc, 
  getDoc,
  orderBy 
} from 'firebase/firestore';
import { db } from '../components/firebase';
import { createFormattedScript } from '../lib/firebase/formatted-scripts';
import { CreateFormattedScriptData } from '../types/formatted-script';

interface ScriptFile {
  id: string;
  name: string;
  namespace: string;
  downloadUrl?: string;
  userId: string;
}

interface MigrationResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
}

/**
 * Get all script files for a user
 */
async function getUserScriptFiles(userEmail: string): Promise<ScriptFile[]> {
  try {
    const filesRef = collection(db, `users/${userEmail}/files`);
    const q = query(
      filesRef,
      where("category", "==", "SceneMate"),
      orderBy("createdAt", "desc")
    );
    
    const querySnapshot = await getDocs(q);
    const files: ScriptFile[] = [];
    
    querySnapshot.forEach((doc) => {
      const fileData = doc.data();
      files.push({
        id: doc.id,
        name: fileData.name || "Untitled Script",
        namespace: fileData.namespace || doc.id,
        downloadUrl: fileData.downloadUrl,
        userId: userEmail
      });
    });
    
    return files;
  } catch (error) {
    console.error('Error fetching user script files:', error);
    throw error;
  }
}

/**
 * Get script content from byteStoreCollection
 */
async function getScriptContent(userEmail: string, namespace: string): Promise<string> {
  try {
    const chunksRef = collection(db, `users/${userEmail}/byteStoreCollection`);
    const q = query(chunksRef, where("metadata.doc_id", "==", namespace));
    const querySnapshot = await getDocs(q);
    const chunks = querySnapshot.docs.map((d) => d.data());

    if (chunks.length === 0) {
      throw new Error(`No content found for namespace: ${namespace}`);
    }

    // Sort chunks by position or page_number
    if ("position" in chunks[0]) {
      chunks.sort((a, b) => (a.position || 0) - (b.position || 0));
    } else if ("metadata" in chunks[0] && "page_number" in chunks[0].metadata) {
      chunks.sort((a, b) => (a.metadata.page_number || 0) - (b.metadata.page_number || 0));
    }

    // Determine content field and assemble
    const contentField = "pageContent" in chunks[0] ? "pageContent" : "content";
    const content = chunks.map((chunk) => chunk[contentField] || "").join("\n");
    
    return content;
  } catch (error) {
    console.error(`Error fetching script content for namespace ${namespace}:`, error);
    throw error;
  }
}

/**
 * Format a single script using the API
 */
async function formatScript(scriptContent: string, fileId: string, namespace: string, userEmail: string, fileName: string): Promise<void> {
  try {
    const response = await fetch('/api/format-script-upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileId: fileId,
        namespace: namespace,
        userId: userEmail,
        fileName: fileName,
        scriptContent: scriptContent
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `API request failed with status ${response.status}`);
    }

    const result = await response.json();
    console.log(`✅ Successfully formatted script: ${fileName} (ID: ${result.formattedScriptId})`);
  } catch (error) {
    console.error(`❌ Failed to format script ${fileName}:`, error);
    throw error;
  }
}

/**
 * Check if a script already has a formatted version
 */
async function hasFormattedVersion(userEmail: string, fileId: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/get-formatted-script/${fileId}?userId=${userEmail}&searchBy=fileId`);
    const result = await response.json();
    return result.success;
  } catch (error) {
    return false;
  }
}

/**
 * Migrate scripts for a specific user
 */
export async function migrateUserScripts(userEmail: string, skipExisting: boolean = true): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: true,
    processed: 0,
    failed: 0,
    errors: []
  };

  try {
    console.log(`🚀 Starting migration for user: ${userEmail}`);
    
    // Get all script files for the user
    const scriptFiles = await getUserScriptFiles(userEmail);
    console.log(`📁 Found ${scriptFiles.length} script files`);

    for (const file of scriptFiles) {
      try {
        // Check if already formatted (if skipExisting is true)
        if (skipExisting && await hasFormattedVersion(userEmail, file.id)) {
          console.log(`⏭️  Skipping ${file.name} - already formatted`);
          continue;
        }

        console.log(`🔄 Processing: ${file.name}`);
        
        // Get script content
        const scriptContent = await getScriptContent(userEmail, file.namespace);
        
        if (!scriptContent || scriptContent.trim().length === 0) {
          throw new Error('Script content is empty');
        }

        // Format the script
        await formatScript(scriptContent, file.id, file.namespace, userEmail, file.name);
        
        result.processed++;
        
        // Add a small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.error(`❌ Failed to process ${file.name}:`, error);
        result.failed++;
        result.errors.push(`${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    console.log(`✅ Migration completed for ${userEmail}`);
    console.log(`📊 Results: ${result.processed} processed, ${result.failed} failed`);
    
    if (result.failed > 0) {
      result.success = false;
    }

  } catch (error) {
    console.error(`💥 Migration failed for user ${userEmail}:`, error);
    result.success = false;
    result.errors.push(`Migration error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return result;
}

/**
 * Migrate all users (admin function)
 */
export async function migrateAllUsers(): Promise<MigrationResult[]> {
  // This would require admin access to list all users
  // Implementation depends on your user management system
  console.log('⚠️  migrateAllUsers requires admin implementation');
  return [];
}

// Example usage:
// import { migrateUserScripts } from './scripts/migrate-existing-scripts';
// 
// // Migrate scripts for a specific user
// migrateUserScripts('<EMAIL>')
//   .then(result => console.log('Migration result:', result))
//   .catch(error => console.error('Migration error:', error));

export default migrateUserScripts;
