// app/layout.tsx
import "styles/globals.css";
import { ReactNode } from "react";
import { SelectedDocProvider } from "components/SelectedDocContext";
import { SessionProvider } from "next-auth/react";

export const metadata = {
  title: 'CastMate-ai',
  description: 'Developed by Indefhubs',
};

export default function RootLayout({
  children,
}: {
  children: ReactNode,
}) {
  return (
    <html lang="en">
      <body className="flex items-center justify-center min-h-screen text-white">
        <SessionProvider>
          <SelectedDocProvider>
            <div className="flex flex-row w-full min-h-screen">
              <div className="h-screen">
                {/* <SideBar /> */}
              </div>
              <div className="flex-grow">
                <div className="max-w-full">{children}</div>
              </div>
            </div>
          </SelectedDocProvider>
        </SessionProvider>
      </body>
    </html>
  );
}