import React from 'react';
import { Loader } from 'lucide-react';

interface CircularProgressProps {
  progress: number; // 0-100
  size?: number; // diameter in pixels
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showPercentage?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export default function CircularProgress({
  progress,
  size = 120,
  strokeWidth = 8,
  color = '#8b5cf6', // purple-500
  backgroundColor = '#374151', // gray-700
  showPercentage = true,
  className = '',
  children
}: CircularProgressProps) {
  // Add padding to prevent clipping of the stroke
  const padding = strokeWidth;
  const svgSize = size + (padding * 2);
  const radius = size / 2 - strokeWidth / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`} style={{ width: svgSize, height: svgSize }}>
      <svg
        width={svgSize}
        height={svgSize}
        className="transform -rotate-90"
        viewBox={`0 0 ${svgSize} ${svgSize}`}
      >
        {/* Background circle */}
        <circle
          cx={svgSize / 2}
          cy={svgSize / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        {/* Progress circle */}
        <circle
          cx={svgSize / 2}
          cy={svgSize / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-300 ease-in-out"
        />
      </svg>

      {/* Content overlay */}
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        {showPercentage && (
          <span className="text-2xl font-bold text-white">
            {Math.round(progress)}%
          </span>
        )}
        {children}
      </div>
    </div>
  );
}

interface ScriptFormattingProgressProps {
  stage: 'uploading' | 'processing' | 'formatting' | 'storing' | 'completed' | 'error';
  progress: number;
  message?: string;
  error?: string;
}

export function ScriptFormattingProgress({
  stage,
  progress,
  message,
  error
}: ScriptFormattingProgressProps) {
  const getStageColor = () => {
    switch (stage) {
      case 'uploading':
        return '#3b82f6'; // blue-500
      case 'processing':
        return '#f59e0b'; // amber-500
      case 'formatting':
        return '#8b5cf6'; // purple-500
      case 'storing':
        return '#10b981'; // emerald-500
      case 'completed':
        return '#22c55e'; // green-500
      case 'error':
        return '#ef4444'; // red-500
      default:
        return '#6b7280'; // gray-500
    }
  };

  const getStageIcon = () => {
    switch (stage) {
      case 'uploading':
        return '📤';
      case 'processing':
        return '⚙️';
      case 'formatting':
        return '✨';
      case 'storing':
        return '💾';
      case 'completed':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '⏳';
    }
  };

  const getStageLabel = () => {
    switch (stage) {
      case 'uploading':
        return 'Uploading';
      case 'processing':
        return 'Processing';
      case 'formatting':
        return 'Formatting';
      case 'storing':
        return 'Storing';
      case 'completed':
        return 'Complete';
      case 'error':
        return 'Error';
      default:
        return 'Working';
    }
  };

  return (
    <div className="flex flex-col items-center space-y-4 p-8 -mt-10">
      <div className="flex items-center justify-center" style={{ minWidth: '180px', minHeight: '180px' }}>
        <CircularProgress
          progress={progress}
          color={getStageColor()}
          size={140}
          strokeWidth={10}
          showPercentage={stage !== 'error'}
        >
          {stage === 'error' ? (
            <span className="text-3xl">{getStageIcon()}</span>
          ) : null}
        </CircularProgress>
      </div>

      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          {stage !== 'completed' && stage !== 'error' ? (
            <Loader className="w-5 h-5 text-amber-300 animate-spin" />
          ) : (
            <span className="text-xl">{getStageIcon()}</span>
          )}
          <span className="text-lg font-semibold text-white">
            {getStageLabel()}
          </span>
        </div>

        {message && (
          <p className="text-sm text-gray-300 max-w-xs">
            {message}
          </p>
        )}

        {error && (
          <p className="text-sm text-red-400 max-w-xs">
            {error}
          </p>
        )}
      </div>
    </div>
  );
}

// Multi-stage progress indicator
interface MultiStageProgressProps {
  stages: Array<{
    id: string;
    label: string;
    icon: string;
  }>;
  currentStage: string;
  progress: number;
  message: string;
}

export function MultiStageProgress({
  stages,
  currentStage,
  progress,
  message
}: MultiStageProgressProps) {
  const currentStageIndex = stages.findIndex(stage => stage.id === currentStage);
  const overallProgress = currentStageIndex >= 0
    ? ((currentStageIndex / (stages.length - 1)) * 100) + (progress / stages.length)
    : 0;

  return (
    <div className="flex flex-col items-center space-y-6 p-6">
      <CircularProgress
        progress={overallProgress}
        size={140}
        strokeWidth={10}
        color="#8b5cf6"
      />

      {/* Stage indicators */}
      <div className="flex items-center space-x-4">
        {stages.map((stage, index) => {
          const isActive = index <= currentStageIndex;
          const isCurrent = stage.id === currentStage;

          return (
            <div key={stage.id} className="flex flex-col items-center space-y-1">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm
                ${isActive ? 'bg-purple-500 text-white' : 'bg-gray-600 text-gray-400'}
                ${isCurrent ? 'ring-2 ring-purple-300' : ''}
              `}>
                {stage.icon}
              </div>
              <span className={`text-xs ${isActive ? 'text-white' : 'text-gray-500'}`}>
                {stage.label}
              </span>
            </div>
          );
        })}
      </div>

      <p className="text-sm text-gray-300 text-center max-w-xs">
        {message}
      </p>
    </div>
  );
}
